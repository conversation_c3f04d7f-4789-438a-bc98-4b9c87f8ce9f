import React from 'react'
import './App.css'

function App() {
  return (
    <div className="App">
      <header className="header">
        <div className="container">
          <div className="logo">
            <h1>166开奖网</h1>
            <p>最快最全的彩票导航信息网</p>
          </div>
          <div className="current-time">
            <span>当前时间：{new Date().toLocaleString('zh-CN')}</span>
          </div>
        </div>
      </header>

      <nav className="nav">
        <div className="container">
          <ul className="nav-list">
            <li><a href="#home" className="active">首页</a></li>
            <li><a href="#lottery">彩票开奖</a></li>
            <li><a href="#trends">走势图</a></li>
            <li><a href="#tools">彩票工具</a></li>
            <li><a href="#news">彩票资讯</a></li>
            <li><a href="#forum">彩票论坛</a></li>
            <li><a href="#help">帮助中心</a></li>
          </ul>
        </div>
      </nav>

      <main className="main">
        <div className="container">
          <section className="lottery-section">
            <h2>热门彩票</h2>
            <div className="lottery-grid">
              <div className="lottery-card">
                <h3>双色球</h3>
                <p className="next-draw">下期开奖：2024-12-15 21:15</p>
                <div className="lottery-actions">
                  <button className="btn-primary">查看开奖</button>
                  <button className="btn-secondary">走势图</button>
                </div>
              </div>
              <div className="lottery-card">
                <h3>大乐透</h3>
                <p className="next-draw">下期开奖：2024-12-16 20:30</p>
                <div className="lottery-actions">
                  <button className="btn-primary">查看开奖</button>
                  <button className="btn-secondary">走势图</button>
                </div>
              </div>
            </div>
          </section>

          <section className="results-section">
            <h2>最新开奖结果</h2>
            <div className="results-table">
              <div className="result-row">
                <div className="lottery-name">双色球</div>
                <div className="period">第2024142期</div>
                <div className="numbers">03 07 12 15 23 28 + 16</div>
                <div className="date">2024-12-14</div>
              </div>
            </div>
          </section>
        </div>
      </main>

      <footer className="footer">
        <div className="container">
          <div className="footer-bottom">
            <p>&copy; 2024 166开奖网 版权所有 | 最快最全的彩票导航信息网</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
